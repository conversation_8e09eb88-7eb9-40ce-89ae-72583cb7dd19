<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Step Wizard + PDF Preview</title>

    <!-- Bootstrap & jQuery -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Dropzone & PDF.js -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />


    <style>
    .wizard-steps {
        display: flex;
        gap: 20px;
        margin: 30px 20px;
        flex-wrap: wrap;
    }

    .wizard-step {
        position: relative;
        display: inline-block;
        padding: 12px 20px;
        min-width: 160px;
        text-align: center;
        background: linear-gradient(to right, #f0f0f0, #e0e0e0);
        clip-path: polygon(0 0, 95% 0, 100% 50%, 95% 100%, 0 100%);
         color: #999;
        font-size: 14px;
        cursor: pointer;
    }

    .wizard-step label {
        font-weight: bold;
        color: #999;
        margin-bottom: 0;
        display: block;
    }

    .wizard-step span {
        font-size: 13px;
        color: #999;
    }

    .wizard-step.active {
        background: linear-gradient(to right, #e6f0fc, #d6e9f9);
    }

    .wizard-step.active label {
        color: #0056b3;
    }

    .wizard-step.active span {
        color: darkred;
    }

    .step-section {
        padding: 20px;
        margin: 0 20px;
        border: 1px solid #ccc;
        border-radius: 6px;
        background-color: #f9f9f9;
    }

    .d-none {
        display: none !important;
    }

    .scrollable-canvas-container {
        height: 565px;
        overflow: auto;
        width: 100%;
        border: 1px solid #ccc;
        padding-bottom: 10px;
        padding-right: 10px;
        position: relative;
    }
    .pdf-viewer-box {
    min-height: 300px; /* decreased height */
    max-width: 1200px; /* increased width */
}

    .scrollable-canvas-container canvas {
        display: block;
        max-width: none;
    }

    #step3Content .col-md-4 {
        min-height: 450px;
    }

    #pageCount {
        font-weight: 500;
    }

    .pdf-controls {
        background-color: #e5e7eb;
        padding: 12px 20px;
        border-radius: 12px;
        margin-top: 20px;
    }

    .pdf-btn {
        background-color: #d1d5db;
        border: none;
        padding: 6px 14px;
        font-size: 14px;
        color: #1f2937;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .pdf-btn:hover {
        background-color: #cbd5e1;
    }

    .pdf-page {
        font-size: 14px;
        color: #1f2937;
        font-weight: 500;
    }

    .pdf-btn, .pdf-page {
        line-height: 1.5rem;
    }
</style>

</head>
<body>

    <div class="container-fluid mt-4">
        <!-- Step Wizard -->
        <div class="wizard-steps">
            <div class="wizard-step active" data-step="1">
                <label>Step 1</label>
                <span>Add Document</span>
            </div>
            <div class="wizard-step" data-step="2">
                <label>Step 2</label>
                <span>Code Info</span>
            </div>
            <div class="wizard-step" data-step="3">
                <label>Step 3</label>
                <span>Heat No Info </span>
            </div>
             <div class="wizard-step" data-step="4">
                <label>Step 4</label>
                <span>Edit AI Response</span>
            </div>
        </div>

        <!-- Step 1 -->
        <div class="step-section" id="step1Content">
            <div class="row">
                <!-- PDF Viewer -->
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3 pdf-viewer-box">

                    <div id="pdfPreviewArea" class="text-center w-100">
                        <div class="scrollable-canvas-container">
                            <canvas id="pdfCanvas"></canvas>
                        </div>
                    </div>
                    <div class="pdf-controls d-flex justify-content-center align-items-center gap-4 mt-3">
                        <button id="zoomOut" class="pdf-btn">Zoom Out</button>
                        <span id="pageCount" class="pdf-page">Page 1 of 1</span>
                        <button id="zoomIn" class="pdf-btn">Zoom In</button>
                        <button id="prevPage" class="pdf-btn">Prev</button>
                        <button id="nextPage" class="pdf-btn">Next</button>
                    </div>

                </div>

                <!-- Upload Form -->
                <div class="col-md-4">
                    <form action="#"
                        method="post"
                        class="dropzone border border-2 border-dashed rounded p-4 text-center mb-3"
                        id="uploadDropzone">
                        <i class="fa fa-upload fs-3 mb-2 d-block text-secondary"></i>
                        <span class="text-secondary">Upload a file or drag and drop<br />
                            <small>(PDF up to 10MB)</small></span>
                    </form>

                    <div class="form-group">
                        <label class="control-label">Name <span class="text-danger fw-bold">*</span> :</label>
                        <input id="txtName" type="text" placeholder="Document Name" class="form-control" />
                    </div>

                    <div class="form-group mt-3">
                        <label class="control-label">Document Type <span class="text-danger fw-bold">*</span> :</label>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="1" name="rbDoc" id="rbDocType1" checked>
                                    <label class="form-check-label" for="rbDocType1">MTR</label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="3" name="rbDoc" id="rbDocType3">
                                    <label class="form-check-label" for="rbDocType3">WPS</label>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="4" name="rbDoc" id="rbDocType4">
                                    <label class="form-check-label" for="rbDocType4">PQR</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label class="control-label">Description</label>
                        <textarea id="txtDescription" class="form-control" placeholder="Description"></textarea>
                    </div>

                    <div class="modal-footer mt-4">
                        <button type="button" class="btn btn-primary w-100" id="btnTryAI">Run MTR AI Validation</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Step 3 -->
        <div class="step-section d-none" id="step2Content">
            <div class="row">
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
                    <div class="scrollable-canvas-container">
                        <canvas id="pdfCanvasStep"></canvas>
                    </div>
                </div>

                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <h6>Select Code Info</h6>
                        <p class="text-muted">Select one or More Code Info values identified by AI.</p>
                        <div id="CodeInfo" class="mb-3"></div>
                    </div>
                    <div class="mt-auto">
                        <button class="btn btn-primary w-100" id="btn1ContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Step 3 -->
        <div class="step-section d-none" id="step3Content">
            <div class="row">
                <div class="col-md-8 d-flex flex-column align-items-center justify-content-start border rounded p-3" style="min-height: 450px;">
                    <div class="scrollable-canvas-container">
                        <canvas id="pdfCanvasStep2"></canvas>
                    </div>
                </div>

                <div class="col-md-4 d-flex flex-column justify-content-between" style="min-height: 100%;">
                    <div>
                        <h6>Select Heat Numbers</h6>
                        <p class="text-muted">The AI found heat numbers. Select which to include in the review.</p>
                        <div id="heatNumberList" class="mb-3"></div>
                    </div>
                    <div class="mt-auto">
                        <button class="btn btn-primary w-100" id="btnContinueReview">Continue to Review</button>
                    </div>
                </div>
            </div>
        </div>
        
      



    <script>
        Dropzone.autoDiscover = false;

        let pdfDoc = null;
        let pdfDocData = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.2;
        const canvas = document.getElementById('pdfCanvas');
        const ctx = canvas.getContext('2d');

        function renderPage(num) {
            pdfDoc.getPage(num).then(function (page) {
                const viewport = page.getViewport({ scale: scale });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                page.render({ canvasContext: ctx, viewport });
                document.getElementById('pageCount').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
            });
        }

        function renderStep2PDF() {
            if (!pdfDocData) return;
            const canvasStep2 = document.getElementById('pdfCanvasStep2');
            const ctx2 = canvasStep2.getContext('2d');
            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                pdf.getPage(1).then(function (page) {
                    const viewport = page.getViewport({ scale: 1.2 });
                    canvasStep2.width = viewport.width;
                    canvasStep2.height = viewport.height;
                    page.render({ canvasContext: ctx2, viewport });
                });
            });
        }

        function populateHeatNumbers(heatNumbers) {
            const container = $('#heatNumberList');
            container.empty();
            if (!heatNumbers || heatNumbers.length === 0) {
                container.append('<p class="text-muted">No heat numbers found.</p>');
                return;
            }

            heatNumbers.forEach((heat, index) => {
                const id = `heat_${index}`;
                container.append(`
        <div class="form-check">
          <input class="form-check-input" type="checkbox" value="${heat}" id="${id}" checked>
          <label class="form-check-label" for="${id}">${heat}</label>
        </div>
      `);
            });
        }

        $(document).ready(function () {
            $('.wizard-step').on('click', function () {
                const step = $(this).data('step');
                $('.wizard-step').removeClass('active');
                $(this).addClass('active');
                $('.step-section').addClass('d-none');
                $('#step' + step + 'Content').removeClass('d-none');

                if (step === 2) {
                    renderStep2PDF();

                    $.ajax({
                        url: '/api/getHeatNumbers',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ filename: uploadedFilename }),
                        success: function (res) {
                            populateHeatNumbers(res.heatNumbers);
                        },
                        error: function () {
                            populateHeatNumbers([]);
                        }
                    });
                }
            });

            const myDropzone = new Dropzone("#uploadDropzone", {
                maxFiles: 1,
                maxFilesize: 10,
                acceptedFiles: ".pdf",
                autoProcessQueue: true,
                addRemoveLinks: true,
                init: function () {
                    this.on("addedfile", function (file) {
                        $('#btnTryAI').prop('disabled', false);
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            pdfDocData = new Uint8Array(e.target.result);
                            pdfjsLib.getDocument({ data: pdfDocData }).promise.then(function (pdf) {
                                pdfDoc = pdf;
                                pageCount = pdf.numPages;
                                pageNum = 1;
                                renderPage(pageNum);
                            });
                        };
                        reader.readAsArrayBuffer(file);
                        $('#btnTryAI').data('uploadedFilename', file.name);
                    });

                    this.on("removedfile", function () {
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        $('#btnTryAI').prop('disabled', true).removeData('uploadedFilename');
                        document.getElementById('pageCount').textContent = 'Page 0 of 0';
                    });
                }
            });

            $('#prevPage').on('click', function () {
                if (pageNum <= 1) return;
                pageNum--;
                renderPage(pageNum);s
            });

            $('#nextPage').on('click', function () {
                if (pageNum >= pdfDoc.numPages) return;
                pageNum++;
                renderPage(pageNum);
            });

            $('#zoomIn').on('click', function () {
                scale += 0.2;
                renderPage(pageNum);
            });

            $('#zoomOut').on('click', function () {
                if (scale > 0.4) {
                    scale -= 0.2;
                    renderPage(pageNum);
                }
            });

            $('#btnTryAI').on('click', function () {
                const uploadedFilename = $(this).data('uploadedFilename');
                if (!uploadedFilename) {
                    alert('No uploaded file available for validation.');
                    return;
                }
                alert("Running AI validation on: " + uploadedFilename);
            });
        });


       
    </script>
</body>
</html>
